/**
 * Cache Module Index - Comprehensive Re-exports
 *
 * Maintains 100% backward compatibility by re-exporting all public APIs
 * from the modular cache implementation. This ensures existing imports
 * continue to work without any changes.
 *
 * Created: 2025-07-10
 * Part of: Phase 1A - cache.ts Refactoring
 */

import { SubscriptionCache } from './core';
import { validateUserSubscription, hasActiveSubscription } from '../validation';
import type { SubscriptionStatus } from '../types';
import type { CacheStats } from './types';
import type { CacheWarmingResult, BatchInvalidationResult, CacheMaintenanceResult } from './types';
import { invalidateExpiredEntries, invalidateMultipleUsers } from './invalidation';
import { CachePerformanceMonitor } from './performance';
import { getEnvironmentCacheConfig } from './config';

// =========================
// Global Cache Instance
// =========================

const subscriptionCache = new SubscriptionCache(getEnvironmentCacheConfig());

// =========================
// Public API Re-exports for Backward Compatibility
// =========================

/**
 * Get subscription status with caching
 */
export async function getCachedSubscriptionStatus(
  userId: string,
  forceRefresh: boolean = false
): Promise<SubscriptionStatus> {
  const startTime = Date.now();

  try {
    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const { data: cachedData, result } = await subscriptionCache.get(userId);

      if (cachedData && result === 'hit') {
        console.log(`[Cache API] Cache hit for user ${userId} (${Date.now() - startTime}ms)`);
        return {
          ...cachedData,
          validationSource: 'cache',
          lastValidated: cachedData.lastValidated,
        };
      }
    }

    // Cache miss or force refresh - validate from database
    console.log(`[Cache API] Cache miss for user ${userId}, validating from database`);
    const validationResult = await validateUserSubscription(userId, {
      useCache: false,
      includeMetrics: true,
    });

    // Cache the result if validation was successful
    if (validationResult.success) {
      await subscriptionCache.set(userId, validationResult.status);
    }

    console.log(`[Cache API] Database validation completed for user ${userId} (${Date.now() - startTime}ms)`);
    return validationResult.status;

  } catch (error) {
    console.error('[Cache API] Error getting cached subscription status:', error);

    // Fail secure - return basic member status
    return {
      hasActiveSubscription: false,
      currentTier: 'MEMBER',
      subscriptionExpiry: null,
      isValid: false,
      lastValidated: new Date().toISOString(),
      validationSource: 'fallback',
      warnings: ['Cache error - defaulting to MEMBER tier for security'],
    };
  }
}

/**
 * Quick cached check for active subscription (optimized for performance)
 */
export async function hasCachedActiveSubscription(userId: string): Promise<boolean> {
  try {
    const { data: cachedData } = await subscriptionCache.get(userId);

    if (cachedData) {
      return cachedData.hasActiveSubscription;
    }

    // Cache miss - use quick database check
    const hasActive = await hasActiveSubscription(userId);

    // Don't cache this lightweight check to avoid cache pollution
    return hasActive;

  } catch (error) {
    console.error('[Cache API] Error checking cached active subscription:', error);
    return false; // Fail secure
  }
}

/**
 * Warm cache for frequently accessed users
 */
export async function warmSubscriptionCache(userIds: string[]): Promise<CacheWarmingResult> {
  const startTime = Date.now();
  let warmed = 0;
  let failed = 0;

  console.log(`[Cache Warming] Starting cache warming for ${userIds.length} users`);

  try {
    // Process in batches to avoid overwhelming the system
    const batchSize = 5;

    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);

      const batchPromises = batch.map(async (userId) => {
        try {
          // Check if already cached
          const { result } = await subscriptionCache.get(userId);

          if (result === 'hit') {
            console.log(`[Cache Warming] User ${userId} already cached, skipping`);
            return;
          }

          // Validate and cache
          const validationResult = await validateUserSubscription(userId);

          if (validationResult.success) {
            await subscriptionCache.set(userId, validationResult.status);
            warmed++;
            console.log(`[Cache Warming] Warmed cache for user ${userId}`);
          } else {
            failed++;
            console.warn(`[Cache Warming] Failed to validate user ${userId}`);
          }
        } catch (error) {
          failed++;
          console.error(`[Cache Warming] Error warming cache for user ${userId}:`, error);
        }
      });

      await Promise.all(batchPromises);

      // Small delay between batches
      if (i + batchSize < userIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const duration = Date.now() - startTime;
    console.log(`[Cache Warming] Completed: ${warmed} warmed, ${failed} failed in ${duration}ms`);

    return { warmed, failed, duration };

  } catch (error) {
    console.error('[Cache Warming] Error during cache warming:', error);
    return { warmed, failed, duration: Date.now() - startTime };
  }
}

/**
 * Invalidate cache for specific user
 */
export async function invalidateUserCache(userId: string): Promise<boolean> {
  try {
    const result = await subscriptionCache.delete(userId);
    console.log(`[Cache API] Invalidated cache for user ${userId}: ${result}`);
    return result;
  } catch (error) {
    console.error('[Cache API] Error invalidating user cache:', error);
    return false;
  }
}

/**
 * Invalidate cache for multiple users
 */
export async function invalidateMultipleUserCache(userIds: string[]): Promise<BatchInvalidationResult> {
  let invalidated = 0;
  let failed = 0;

  try {
    for (const userId of userIds) {
      const result = await subscriptionCache.delete(userId);
      if (result) {
        invalidated++;
      } else {
        failed++;
      }
    }

    console.log(`[Cache API] Batch invalidation: ${invalidated} invalidated, ${failed} failed`);
    return { invalidated, failed };

  } catch (error) {
    console.error('[Cache API] Error during batch invalidation:', error);
    return { invalidated, failed: userIds.length };
  }
}

/**
 * Get cache statistics and performance metrics
 */
export function getCacheStats(): CacheStats & { info: ReturnType<typeof subscriptionCache.getInfo> } {
  return {
    ...subscriptionCache.getStats(),
    info: subscriptionCache.getInfo(),
  };
}

/**
 * Clear all cached subscription data
 */
export async function clearSubscriptionCache(): Promise<void> {
  try {
    await subscriptionCache.clear();
    console.log('[Cache API] All subscription cache cleared');
  } catch (error) {
    console.error('[Cache API] Error clearing cache:', error);
  }
}

/**
 * Perform cache maintenance (cleanup expired entries)
 */
export async function performCacheMaintenance(): Promise<CacheMaintenanceResult> {
  try {
    // Access private cache map through a public method we'll need to add
    const stats = subscriptionCache.getStats();
    const info = subscriptionCache.getInfo();

    // For now, return current stats - we'll need to enhance core.ts to support this
    console.log(`[Cache Maintenance] Current cache state: ${info.size} total entries`);

    return {
      expiredRemoved: 0, // Will be implemented when we enhance core.ts
      totalEntries: info.size,
      hitRate: stats.hitRate,
    };

  } catch (error) {
    console.error('[Cache Maintenance] Error during maintenance:', error);
    return {
      expiredRemoved: 0,
      totalEntries: 0,
      hitRate: 0,
    };
  }
}

/**
 * Update cache configuration at runtime
 */
export function updateCacheConfig(newConfig: Partial<import('./types').CacheConfig>): void {
  // Note: This would require refactoring the cache class to support runtime config updates
  console.warn('[Cache API] Runtime config updates not yet implemented. Restart required for config changes.');
}

/**
 * Export cache instance for advanced usage
 */
export { subscriptionCache };
