/**
 * Role Classification System for Subscription Enforcement
 * 
 * This module provides role classification logic to determine which roles should
 * be exempt from subscription validation (administrative roles) versus which roles
 * should require active subscription validation (user roles).
 * 
 * Created: 2025-01-08
 * Part of: Phase 3.1 - Administrative Role Exemption System
 * 
 * ROLE CLASSIFICATION STRATEGY:
 * - ADMINISTRATIVE_EXEMPT: Platform Owner, Store Owner, Store Manager
 *   These roles are business-critical and must maintain access for operational continuity
 * 
 * - USER_ENFORCED: Club Leadership, Club Moderator  
 *   These roles provide premium features and should require active subscriptions
 */

import { supabase } from '../supabase';

// =========================
// Type Definitions
// =========================

export interface RoleClassification {
  userId: string;
  administrativeRoles: AdministrativeRole[];
  userRoles: UserRole[];
  requiresSubscriptionValidation: boolean;
  exemptFromValidation: boolean;
  classificationReason: string;
}

export interface AdministrativeRole {
  type: 'PLATFORM_OWNER' | 'STORE_OWNER' | 'STORE_MANAGER';
  storeId?: string;
  grantedAt: string;
  source: string;
}

export interface UserRole {
  type: 'CLUB_LEADERSHIP' | 'CLUB_MODERATOR';
  clubId: string;
  grantedAt: string;
  requiresSubscription: boolean;
}

export interface SubscriptionValidationDecision {
  shouldValidate: boolean;
  reason: string;
  exemptRoles: AdministrativeRole[];
  enforcedRoles: UserRole[];
}

// =========================
// Role Classification Constants
// =========================

/**
 * Administrative roles that are exempt from subscription validation
 * These roles are critical for business operations and platform management
 */
export const ADMINISTRATIVE_EXEMPT_ROLES = [
  'PLATFORM_OWNER',
  'STORE_OWNER', 
  'STORE_MANAGER'
] as const;

/**
 * User roles that require active subscription validation
 * These roles provide premium features and should be subscription-gated
 */
export const USER_ENFORCED_ROLES = [
  'CLUB_LEADERSHIP',
  'CLUB_MODERATOR'
] as const;

// =========================
// Core Classification Functions
// =========================

/**
 * Determines if a role type is administrative and exempt from subscription validation
 * 
 * @param roleType - The role type to check
 * @returns boolean - True if role is administrative and exempt
 */
export function isAdministrativeRole(roleType: string): boolean {
  return ADMINISTRATIVE_EXEMPT_ROLES.includes(roleType as any);
}

/**
 * Determines if a role type requires subscription validation
 * 
 * @param roleType - The role type to check  
 * @returns boolean - True if role requires subscription validation
 */
export function requiresSubscriptionValidation(roleType: string): boolean {
  return USER_ENFORCED_ROLES.includes(roleType as any);
}

/**
 * Checks if a user has any administrative roles that exempt them from subscription validation
 * 
 * @param userId - User ID to check
 * @returns Promise<boolean> - True if user has administrative exemption
 */
export async function hasAdministrativeExemption(userId: string): Promise<boolean> {
  try {
    // Check platform owner status
    const isPlatformOwner = await checkPlatformOwnerStatus(userId);
    if (isPlatformOwner) {
      return true;
    }

    // Check store administrator roles
    const { data: storeRoles, error: storeError } = await supabase
      .from('store_administrators')
      .select('role')
      .eq('user_id', userId);

    if (storeError) {
      console.warn('[Role Classification] Error checking store roles:', storeError);
      return false;
    }

    // Check if user has store owner or manager role
    const hasStoreAdminRole = storeRoles?.some(role => 
      role.role === 'owner' || role.role === 'manager'
    );

    return hasStoreAdminRole || false;

  } catch (error) {
    console.error('[Role Classification] Error checking administrative exemption:', error);
    return false; // Fail secure - no exemption if error
  }
}

/**
 * Checks if a user has any user roles that require subscription validation
 * 
 * @param userId - User ID to check
 * @returns Promise<boolean> - True if user has roles requiring validation
 */
export async function hasUserRolesRequiringValidation(userId: string): Promise<boolean> {
  try {
    // Check club leadership
    const { data: ledClubs, error: clubError } = await supabase
      .from('book_clubs')
      .select('id')
      .eq('lead_user_id', userId);

    if (clubError) {
      console.warn('[Role Classification] Error checking club leadership:', clubError);
    }

    const hasClubLeadership = (ledClubs?.length || 0) > 0;

    // Check club moderator roles
    const { data: moderatedClubs, error: modError } = await supabase
      .from('club_moderators')
      .select('club_id')
      .eq('user_id', userId);

    if (modError) {
      console.warn('[Role Classification] Error checking club moderation:', modError);
    }

    const hasClubModeration = (moderatedClubs?.length || 0) > 0;

    return hasClubLeadership || hasClubModeration;

  } catch (error) {
    console.error('[Role Classification] Error checking user roles:', error);
    return false; // Fail secure - no enforcement if error
  }
}

/**
 * Comprehensive role classification for a user
 * 
 * @param userId - User ID to classify
 * @returns Promise<RoleClassification> - Complete role classification
 */
export async function classifyUserRoles(userId: string): Promise<RoleClassification> {
  try {
    const administrativeRoles: AdministrativeRole[] = [];
    const userRoles: UserRole[] = [];

    // Check platform owner
    const isPlatformOwner = await checkPlatformOwnerStatus(userId);
    if (isPlatformOwner) {
      administrativeRoles.push({
        type: 'PLATFORM_OWNER',
        grantedAt: new Date().toISOString(),
        source: 'platform_settings'
      });
    }

    // Check store roles
    const { data: storeRoles } = await supabase
      .from('store_administrators')
      .select('store_id, role')
      .eq('user_id', userId);

    for (const storeRole of storeRoles || []) {
      if (storeRole.role === 'owner') {
        administrativeRoles.push({
          type: 'STORE_OWNER',
          storeId: storeRole.store_id,
          grantedAt: new Date().toISOString(),
          source: 'store_administrators'
        });
      } else if (storeRole.role === 'manager') {
        administrativeRoles.push({
          type: 'STORE_MANAGER',
          storeId: storeRole.store_id,
          grantedAt: new Date().toISOString(),
          source: 'store_administrators'
        });
      }
    }

    // Check club leadership
    const { data: ledClubs } = await supabase
      .from('book_clubs')
      .select('id, created_at')
      .eq('lead_user_id', userId);

    for (const club of ledClubs || []) {
      userRoles.push({
        type: 'CLUB_LEADERSHIP',
        clubId: club.id,
        grantedAt: club.created_at || new Date().toISOString(),
        requiresSubscription: true
      });
    }

    // Check club moderation
    const { data: moderatedClubs } = await supabase
      .from('club_moderators')
      .select('club_id')
      .eq('user_id', userId);

    for (const club of moderatedClubs || []) {
      userRoles.push({
        type: 'CLUB_MODERATOR',
        clubId: club.club_id,
        grantedAt: new Date().toISOString(),
        requiresSubscription: true
      });
    }

    // Determine classification
    const exemptFromValidation = administrativeRoles.length > 0;
    const requiresSubscriptionValidation = !exemptFromValidation && userRoles.length > 0;

    let classificationReason: string;
    if (exemptFromValidation) {
      classificationReason = `Administrative exemption: ${administrativeRoles.map(r => r.type).join(', ')}`;
    } else if (requiresSubscriptionValidation) {
      classificationReason = `User role enforcement: ${userRoles.map(r => r.type).join(', ')}`;
    } else {
      classificationReason = 'No special roles - standard member';
    }

    return {
      userId,
      administrativeRoles,
      userRoles,
      requiresSubscriptionValidation,
      exemptFromValidation,
      classificationReason
    };

  } catch (error) {
    console.error('[Role Classification] Error classifying user roles:', error);
    
    // Fail secure classification
    return {
      userId,
      administrativeRoles: [],
      userRoles: [],
      requiresSubscriptionValidation: false,
      exemptFromValidation: false,
      classificationReason: 'Classification error - defaulting to standard member'
    };
  }
}

/**
 * Makes subscription validation decision for role-based entitlements
 * 
 * @param userId - User ID to make decision for
 * @returns Promise<SubscriptionValidationDecision> - Validation decision
 */
export async function makeSubscriptionValidationDecision(
  userId: string
): Promise<SubscriptionValidationDecision> {
  try {
    const classification = await classifyUserRoles(userId);

    return {
      shouldValidate: classification.requiresSubscriptionValidation,
      reason: classification.classificationReason,
      exemptRoles: classification.administrativeRoles,
      enforcedRoles: classification.userRoles
    };

  } catch (error) {
    console.error('[Role Classification] Error making validation decision:', error);
    
    // Fail secure - require validation if error
    return {
      shouldValidate: true,
      reason: 'Error in classification - defaulting to validation required',
      exemptRoles: [],
      enforcedRoles: []
    };
  }
}

// =========================
// Helper Functions
// =========================

/**
 * Checks if user is platform owner
 * 
 * @param userId - User ID to check
 * @returns Promise<boolean> - True if user is platform owner
 */
async function checkPlatformOwnerStatus(userId: string): Promise<boolean> {
  try {
    const { data: platformOwner } = await supabase
      .from('platform_settings')
      .select('value')
      .eq('key', 'platform_owner_id')
      .single();

    return platformOwner?.value === userId;
  } catch (error) {
    console.warn('[Role Classification] Could not check platform owner status:', error);
    return false;
  }
}

/**
 * Logs role classification decision for monitoring
 * 
 * @param userId - User ID
 * @param decision - Validation decision
 * @param context - Additional context
 */
export function logRoleClassificationDecision(
  userId: string,
  decision: SubscriptionValidationDecision,
  context: string = 'entitlement_calculation'
): void {
  console.log(`[Role Classification] Decision for user ${userId}:`, {
    shouldValidate: decision.shouldValidate,
    reason: decision.reason,
    exemptRoles: decision.exemptRoles.length,
    enforcedRoles: decision.enforcedRoles.length,
    context
  });
}
