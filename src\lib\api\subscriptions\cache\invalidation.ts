/**
 * Cache Invalidation Module
 *
 * Handles subscription-based invalidation, event-driven cache clearing,
 * and cleanup procedures. Extracted from cache.ts refactoring.
 *
 * Created: 2025-07-10
 * Part of: Phase 1A - cache.ts Refactoring
 */

import type { CacheEntry } from './types';
import { CACHE_CLEANUP_INTERVAL } from './types';

// =========================
// Cache Invalidation Functions
// =========================

/**
 * Invalidate cache entries for users with expired subscriptions
 */
export function invalidateExpiredEntries(
  cache: Map<string, CacheEntry>
): number {
  let invalidatedCount = 0;
  const now = Date.now();

  try {
    // Convert to array to avoid iterator issues
    const entries = Array.from(cache.entries());
    for (const [key, entry] of entries) {
      let shouldInvalidate = false;

      // Check cache expiry
      if (entry.expiresAt <= now) {
        shouldInvalidate = true;
      }

      // Check subscription expiry
      if (entry.subscriptionExpiry) {
        const subscriptionExpiry = new Date(entry.subscriptionExpiry).getTime();
        if (subscriptionExpiry <= now) {
          shouldInvalidate = true;
        }
      }

      if (shouldInvalidate) {
        cache.delete(key);
        invalidatedCount++;
      }
    }

    if (invalidatedCount > 0) {
      console.log(`[Cache Invalidation] Invalidated ${invalidatedCount} expired entries`);
    }

    return invalidatedCount;
  } catch (error) {
    console.error('[Cache Invalidation] Error invalidating expired entries:', error);
    return 0;
  }
}

/**
 * Invalidate cache entries for multiple users
 */
export function invalidateMultipleUsers(
  cache: Map<string, CacheEntry>,
  userIds: string[],
  keyPrefix: string
): { invalidated: number; failed: number } {
  let invalidated = 0;
  let failed = 0;

  try {
    for (const userId of userIds) {
      const key = `${keyPrefix}${userId}`;
      if (cache.delete(key)) {
        invalidated++;
      } else {
        failed++;
      }
    }

    console.log(`[Cache Invalidation] Batch invalidation: ${invalidated} invalidated, ${failed} failed`);
    return { invalidated, failed };

  } catch (error) {
    console.error('[Cache Invalidation] Error during batch invalidation:', error);
    return { invalidated, failed: userIds.length };
  }
}

/**
 * Setup automatic cleanup interval for expired entries
 */
export function setupCleanupInterval(
  cleanupFunction: () => number
): NodeJS.Timeout {
  return setInterval(() => {
    cleanupFunction();
  }, CACHE_CLEANUP_INTERVAL);
}

/**
 * Cleanup expired cache entries
 */
export function cleanupExpiredEntries(
  cache: Map<string, CacheEntry>
): number {
  let removedCount = 0;
  const now = Date.now();

  // Convert to array to avoid iterator issues
  const entries = Array.from(cache.entries());
  for (const [key, entry] of entries) {
    let shouldRemove = false;

    // Check cache expiry
    if (entry.expiresAt <= now) {
      shouldRemove = true;
    }

    // Check subscription expiry
    if (entry.subscriptionExpiry) {
      const subscriptionExpiry = new Date(entry.subscriptionExpiry).getTime();
      if (subscriptionExpiry <= now) {
        shouldRemove = true;
      }
    }

    if (shouldRemove) {
      cache.delete(key);
      removedCount++;
    }
  }

  if (removedCount > 0) {
    console.log(`[Cache Cleanup] Removed ${removedCount} expired entries`);
  }

  return removedCount;
}
