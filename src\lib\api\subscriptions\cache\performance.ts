/**
 * Cache Performance Monitoring Module
 *
 * Handles performance monitoring, cache hit/miss tracking, optimization algorithms,
 * and metrics collection. Extracted from cache.ts refactoring.
 *
 * Created: 2025-07-10
 * Part of: Phase 1A - cache.ts Refactoring
 */

import type { CacheStats } from './types';

// =========================
// Performance Monitoring
// =========================

/**
 * Performance monitor for cache operations
 */
export class CachePerformanceMonitor {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
    hitRate: 0,
    averageResponseTime: 0,
  };

  /**
   * Record a cache hit
   */
  recordHit(responseTime: number): void {
    this.stats.hits++;
    this.stats.totalRequests++;
    this.updateAverageResponseTime(responseTime);
    this.updateHitRate();
  }

  /**
   * Record a cache miss
   */
  recordMiss(responseTime: number): void {
    this.stats.misses++;
    this.stats.totalRequests++;
    this.updateAverageResponseTime(responseTime);
    this.updateHitRate();
  }

  /**
   * Record a cache eviction
   */
  recordEviction(): void {
    this.stats.evictions++;
  }

  /**
   * Get current performance statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Reset performance statistics
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0,
      hitRate: 0,
      averageResponseTime: 0,
    };
  }

  /**
   * Update average response time
   */
  private updateAverageResponseTime(responseTime: number): void {
    this.stats.averageResponseTime =
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests;
  }

  /**
   * Update hit rate
   */
  private updateHitRate(): void {
    this.stats.hitRate = this.stats.hits / this.stats.totalRequests;
  }
}

// =========================
// Performance Utilities
// =========================

/**
 * Calculate cache efficiency metrics
 */
export function calculateCacheEfficiency(stats: CacheStats): {
  efficiency: number;
  recommendation: string;
} {
  const { hitRate, averageResponseTime, evictions, totalRequests } = stats;

  let efficiency = 0;
  let recommendation = '';

  // Calculate efficiency score (0-100)
  if (totalRequests === 0) {
    efficiency = 0;
    recommendation = 'No cache activity recorded';
  } else {
    // Hit rate contributes 60% to efficiency
    const hitRateScore = hitRate * 60;

    // Response time contributes 25% (lower is better)
    const responseTimeScore = Math.max(0, 25 - (averageResponseTime / 10));

    // Eviction rate contributes 15% (lower is better)
    const evictionRate = evictions / totalRequests;
    const evictionScore = Math.max(0, 15 - (evictionRate * 100));

    efficiency = Math.round(hitRateScore + responseTimeScore + evictionScore);

    // Generate recommendations
    if (hitRate < 0.7) {
      recommendation = 'Consider increasing cache TTL or cache size';
    } else if (averageResponseTime > 50) {
      recommendation = 'Cache operations are slow - consider optimization';
    } else if (evictionRate > 0.1) {
      recommendation = 'High eviction rate - consider increasing cache size';
    } else {
      recommendation = 'Cache performance is optimal';
    }
  }

  return { efficiency, recommendation };
}
