/**
 * Subscription Validation Functions
 * 
 * Core validation functions for the BookTalks Buddy subscription system.
 * Implements fail-secure error handling and integrates with existing database functions.
 * 
 * Created: 2025-01-07
 * Part of: Phase 1 - Foundation & API Layer
 */

import { supabase } from '@/lib/supabase';
import {
  SubscriptionStatus,
  SubscriptionValidationResult,
  EntitlementValidationResult,
  ValidationError,
  ValidationOptions,
  ValidationPerformanceMetrics,
  DEFAULT_VALIDATION_OPTIONS,
  MAX_VALIDATION_TIMEOUT,
} from './types';

// =========================
// Core Validation Functions
// =========================

/**
 * Validates a user's subscription status with comprehensive error handling
 * 
 * @param userId - The user ID to validate
 * @param options - Validation options (timeout, cache, etc.)
 * @returns Promise<SubscriptionValidationResult> - Complete validation result
 */
export async function validateUserSubscription(
  userId: string,
  options: ValidationOptions = {}
): Promise<SubscriptionValidationResult> {
  const startTime = Date.now();
  const validationOptions = { ...DEFAULT_VALIDATION_OPTIONS, ...options };
  const errors: ValidationError[] = [];
  
  // Validate input parameters
  if (!userId || typeof userId !== 'string') {
    const error = createValidationError(
      'INVALID_USER_ID',
      'User ID is required and must be a string',
      { userId },
      'high'
    );
    return createFailSecureValidationResult(userId, [error], startTime);
  }

  // Apply timeout protection
  const timeoutMs = Math.min(validationOptions.timeout || 5000, MAX_VALIDATION_TIMEOUT);
  
  try {
    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Validation timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    // Create validation promise
    const validationPromise = performSubscriptionValidation(userId, validationOptions);

    // Race between validation and timeout
    const result = await Promise.race([validationPromise, timeoutPromise]);
    
    const endTime = Date.now();
    const performanceMetrics: ValidationPerformanceMetrics = {
      queryTime: endTime - startTime,
      cacheHit: result.status.validationSource === 'cache',
      queryCount: result.queryCount || 0,
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
    };

    return {
      userId,
      status: result.status,
      errors: result.errors,
      performanceMetrics,
      success: result.errors.length === 0,
    };

  } catch (error) {
    console.error('Subscription validation failed:', error);
    
    const validationError = createValidationError(
      'VALIDATION_FAILED',
      error instanceof Error ? error.message : 'Unknown validation error',
      { userId, error: String(error) },
      'critical'
    );

    return createFailSecureValidationResult(userId, [validationError], startTime);
  }
}

/**
 * Performs the actual subscription validation logic
 * 
 * @param userId - User ID to validate
 * @param options - Validation options
 * @returns Promise with validation status and metadata
 */
async function performSubscriptionValidation(
  userId: string,
  options: ValidationOptions
): Promise<{ status: SubscriptionStatus; errors: ValidationError[]; queryCount: number }> {
  const errors: ValidationError[] = [];
  let queryCount = 0;
  
  try {
    // Step 1: Check if user has active subscription
    console.log(`[Validation] Checking active subscription for user: ${userId}`);
    const { data: hasActive, error: activeError } = await supabase
      .rpc('has_active_subscription', { p_user_id: userId });
    
    queryCount++;

    if (activeError) {
      console.error('Active subscription check failed:', activeError);
      const error = createValidationError(
        'ACTIVE_SUBSCRIPTION_CHECK_FAILED',
        `Failed to check active subscription: ${activeError.message}`,
        { userId, supabaseError: activeError },
        'high'
      );
      errors.push(error);
      
      // Fail secure - return member status
      return {
        status: createFailSecureSubscriptionStatus(userId, 'database_error'),
        errors,
        queryCount,
      };
    }

    // Step 2: Get current subscription tier
    console.log(`[Validation] Getting subscription tier for user: ${userId}`);
    const { data: currentTier, error: tierError } = await supabase
      .rpc('get_user_subscription_tier', { p_user_id: userId });
    
    queryCount++;

    if (tierError) {
      console.error('Subscription tier check failed:', tierError);
      const error = createValidationError(
        'SUBSCRIPTION_TIER_CHECK_FAILED',
        `Failed to get subscription tier: ${tierError.message}`,
        { userId, supabaseError: tierError },
        'high'
      );
      errors.push(error);
      
      // Fail secure - return member status
      return {
        status: createFailSecureSubscriptionStatus(userId, 'database_error'),
        errors,
        queryCount,
      };
    }

    // Step 3: Get subscription expiry date
    console.log(`[Validation] Getting subscription details for user: ${userId}`);
    const { data: subscription, error: subError } = await supabase
      .from('user_subscriptions')
      .select('end_date, tier, subscription_type')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('end_date', { ascending: false })
      .limit(1)
      .maybeSingle(); // Use maybeSingle to handle no results gracefully
    
    queryCount++;

    if (subError) {
      console.error('Subscription details check failed:', subError);
      const error = createValidationError(
        'SUBSCRIPTION_DETAILS_CHECK_FAILED',
        `Failed to get subscription details: ${subError.message}`,
        { userId, supabaseError: subError },
        'medium'
      );
      errors.push(error);
      // Continue with validation using available data
    }

    // Step 4: Validate subscription status
    const subscriptionExpiry = subscription?.end_date || null;
    const isExpired = subscriptionExpiry ? new Date(subscriptionExpiry) <= new Date() : false;
    
    if (hasActive && isExpired) {
      const warning = createValidationError(
        'SUBSCRIPTION_EXPIRED',
        'User has active subscription flag but subscription is expired',
        { userId, subscriptionExpiry, hasActive },
        'medium'
      );
      errors.push(warning);
    }

    // Step 5: Create subscription status
    const validatedTier = validateTier(currentTier);
    const isValid = hasActive && !isExpired && validatedTier !== 'MEMBER';

    const status: SubscriptionStatus = {
      hasActiveSubscription: hasActive || false,
      currentTier: validatedTier,
      subscriptionExpiry,
      isValid,
      lastValidated: new Date().toISOString(),
      validationSource: 'database',
      warnings: errors.filter(e => e.severity === 'medium').map(e => e.message),
    };

    console.log(`[Validation] Completed for user ${userId}:`, {
      hasActive,
      currentTier: validatedTier,
      isValid,
      queryCount,
      errorCount: errors.length,
    });

    return { status, errors, queryCount };

  } catch (error) {
    console.error('Unexpected error during subscription validation:', error);
    
    const validationError = createValidationError(
      'UNEXPECTED_VALIDATION_ERROR',
      error instanceof Error ? error.message : 'Unexpected validation error',
      { userId, error: String(error) },
      'critical'
    );

    return {
      status: createFailSecureSubscriptionStatus(userId, 'system_error'),
      errors: [validationError],
      queryCount,
    };
  }
}

/**
 * Validates and corrects user entitlements using the backend function
 * 
 * @param userId - User ID to validate entitlements for
 * @returns Promise<EntitlementValidationResult> - Entitlement validation result
 */
export async function validateUserEntitlements(
  userId: string
): Promise<EntitlementValidationResult> {
  try {
    console.log(`[Entitlements] Validating entitlements for user: ${userId}`);
    
    const { data, error } = await supabase
      .rpc('validate_user_entitlements', { p_user_id: userId });
    
    if (error) {
      console.error('Entitlement validation error:', error);
      throw new Error(`Entitlement validation failed: ${error.message}`);
    }

    const result: EntitlementValidationResult = {
      userId: data.user_id,
      currentTier: data.current_membership_tier,
      subscriptionTier: data.subscription_tier,
      hasActiveSubscription: data.has_active_subscription,
      needsUpdate: data.needs_update,
      updatedTier: data.updated_tier,
      issues: data.issues || [],
      success: true,
    };

    console.log(`[Entitlements] Validation completed for user ${userId}:`, {
      needsUpdate: result.needsUpdate,
      currentTier: result.currentTier,
      subscriptionTier: result.subscriptionTier,
      issueCount: result.issues.length,
    });

    return result;
    
  } catch (error) {
    console.error('Error validating entitlements:', error);
    
    // Return fail-secure result
    return {
      userId,
      currentTier: 'MEMBER',
      subscriptionTier: 'MEMBER',
      hasActiveSubscription: false,
      needsUpdate: false,
      updatedTier: null,
      issues: [`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      success: false,
    };
  }
}

// =========================
// Utility Functions
// =========================

/**
 * Creates a validation error object with consistent structure
 */
function createValidationError(
  code: string,
  message: string,
  details: any,
  severity: 'low' | 'medium' | 'high' | 'critical'
): ValidationError {
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString(),
    severity,
  };
}

/**
 * Creates a fail-secure subscription status for error conditions
 */
function createFailSecureSubscriptionStatus(
  userId: string,
  errorType: string
): SubscriptionStatus {
  return {
    hasActiveSubscription: false,
    currentTier: 'MEMBER',
    subscriptionExpiry: null,
    isValid: false,
    lastValidated: new Date().toISOString(),
    validationSource: 'fallback',
    warnings: [`Validation failed (${errorType}) - defaulting to MEMBER tier for security`],
  };
}

/**
 * Creates a fail-secure validation result for error conditions
 */
function createFailSecureValidationResult(
  userId: string,
  errors: ValidationError[],
  startTime: number
): SubscriptionValidationResult {
  const endTime = Date.now();
  
  return {
    userId,
    status: createFailSecureSubscriptionStatus(userId, 'validation_error'),
    errors,
    performanceMetrics: {
      queryTime: endTime - startTime,
      cacheHit: false,
      queryCount: 0,
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
    },
    success: false,
  };
}

/**
 * Validates and normalizes subscription tier values
 */
function validateTier(tier: any): 'MEMBER' | 'PRIVILEGED' | 'PRIVILEGED_PLUS' {
  if (typeof tier !== 'string') {
    return 'MEMBER';
  }

  const normalizedTier = tier.toUpperCase();

  if (normalizedTier === 'PRIVILEGED' || normalizedTier === 'PRIVILEGED_PLUS') {
    return normalizedTier as 'PRIVILEGED' | 'PRIVILEGED_PLUS';
  }

  return 'MEMBER';
}

// =========================
// Batch Validation Functions
// =========================

/**
 * Validates multiple users' subscriptions in batch
 *
 * @param userIds - Array of user IDs to validate
 * @param options - Validation options
 * @returns Promise<SubscriptionValidationResult[]> - Array of validation results
 */
export async function batchValidateSubscriptions(
  userIds: string[],
  options: ValidationOptions = {}
): Promise<SubscriptionValidationResult[]> {
  console.log(`[Batch Validation] Starting batch validation for ${userIds.length} users`);

  const results: SubscriptionValidationResult[] = [];
  const batchSize = 10; // Process in batches to avoid overwhelming the database

  for (let i = 0; i < userIds.length; i += batchSize) {
    const batch = userIds.slice(i, i + batchSize);
    console.log(`[Batch Validation] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(userIds.length / batchSize)}`);

    const batchPromises = batch.map(userId =>
      validateUserSubscription(userId, options).catch(error => {
        console.error(`Batch validation failed for user ${userId}:`, error);
        return createFailSecureValidationResult(userId, [
          createValidationError(
            'BATCH_VALIDATION_FAILED',
            `Batch validation failed: ${error.message}`,
            { userId, error: String(error) },
            'high'
          )
        ], Date.now());
      })
    );

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Small delay between batches to prevent overwhelming the database
    if (i + batchSize < userIds.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`[Batch Validation] Completed batch validation. Success rate: ${results.filter(r => r.success).length}/${results.length}`);

  return results;
}

/**
 * Quick subscription status check (minimal data, faster response)
 *
 * @param userId - User ID to check
 * @returns Promise<boolean> - True if user has active subscription
 */
export async function hasActiveSubscription(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .rpc('has_active_subscription_simple', { p_user_id: userId });

    if (error) {
      console.error('Active subscription check failed:', error);
      return false; // Fail secure
    }

    return data || false;
  } catch (error) {
    console.error('Active subscription check failed:', error);
    return false; // Fail secure
  }
}

/**
 * Get user's current subscription tier (quick check)
 *
 * @param userId - User ID to check
 * @returns Promise<string> - User's subscription tier
 */
export async function getUserSubscriptionTier(userId: string): Promise<string> {
  try {
    const { data, error } = await supabase
      .rpc('get_user_subscription_tier', { p_user_id: userId });

    if (error) {
      console.error('Tier check failed:', error);
      return 'MEMBER'; // Fail secure
    }

    return validateTier(data);
  } catch (error) {
    console.error('Tier check error:', error);
    return 'MEMBER'; // Fail secure
  }
}
