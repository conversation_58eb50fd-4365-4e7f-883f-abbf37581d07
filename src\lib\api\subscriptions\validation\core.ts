/**
 * Core Validation Engine Module
 *
 * Implements the main validation logic, fail-secure mechanisms, and validation
 * orchestration. Extracted from validation.ts refactoring.
 *
 * Created: 2025-07-10
 * Part of: Phase 1B - validation.ts Refactoring
 */

import { supabase } from '@/lib/supabase';
import type { SubscriptionStatus } from '../types';
import type {
  ValidationContext,
  InternalValidationResult,
  ValidationStepResult,
} from './types';
import { VALIDATION_ERROR_CODES, FAIL_SECURE_REASONS } from './types';
import { createValidationError, createFailSecureSubscriptionStatus } from './error-handling';
import { validateTier } from './utils';

// =========================
// Core Validation Engine
// =========================

/**
 * Performs the main subscription validation logic
 */
export async function performSubscriptionValidation(
  context: ValidationContext
): Promise<InternalValidationResult> {
  const { userId, options } = context;
  let queryCount = 0;
  const errors = [...context.errors];

  try {
    // Step 1: Check if user has active subscription
    console.log(`[Validation] Checking active subscription for user: ${userId}`);
    const activeResult = await checkActiveSubscription(userId);
    queryCount += activeResult.queryCount;

    if (!activeResult.success) {
      errors.push(activeResult.error!);

      if (context.failSecure) {
        return {
          status: createFailSecureSubscriptionStatus(userId, FAIL_SECURE_REASONS.DATABASE_ERROR),
          errors,
          queryCount,
        };
      }
    }

    const hasActive = activeResult.data || false;

    // Step 2: Get subscription tier
    console.log(`[Validation] Getting subscription tier for user: ${userId}`);
    const tierResult = await getSubscriptionTier(userId);
    queryCount += tierResult.queryCount;

    if (!tierResult.success) {
      errors.push(tierResult.error!);
      // Continue with validation using default tier
    }

    const currentTier = tierResult.data || 'MEMBER';

    // Step 3: Get subscription details
    console.log(`[Validation] Getting subscription details for user: ${userId}`);
    const detailsResult = await getSubscriptionDetails(userId);
    queryCount += detailsResult.queryCount;

    if (!detailsResult.success) {
      errors.push(detailsResult.error!);
      // Continue with validation using available data
    }

    const subscription = detailsResult.data;

    // Step 4: Validate subscription status
    const validationResult = validateSubscriptionStatus({
      userId,
      hasActive,
      currentTier,
      subscription,
      errors,
    });

    console.log(`[Validation] Completed for user ${userId}:`, {
      hasActive,
      currentTier: validationResult.status.currentTier,
      isValid: validationResult.status.isValid,
      queryCount,
      errorCount: validationResult.errors.length,
    });

    return {
      status: validationResult.status,
      errors: validationResult.errors,
      queryCount,
    };

  } catch (error) {
    console.error('Unexpected error during subscription validation:', error);

    const validationError = createValidationError(
      VALIDATION_ERROR_CODES.UNEXPECTED_VALIDATION_ERROR,
      error instanceof Error ? error.message : 'Unexpected validation error',
      { userId, error: String(error) },
      'critical'
    );

    return {
      status: createFailSecureSubscriptionStatus(userId, FAIL_SECURE_REASONS.SYSTEM_ERROR),
      errors: [validationError],
      queryCount,
    };
  }
}

// =========================
// Validation Steps
// =========================

/**
 * Check if user has active subscription
 */
async function checkActiveSubscription(userId: string): Promise<ValidationStepResult> {
  try {
    const { data: hasActive, error: activeError } = await supabase
      .rpc('has_active_subscription', { p_user_id: userId });

    if (activeError) {
      console.error('Active subscription check failed:', activeError);
      return {
        success: false,
        error: createValidationError(
          VALIDATION_ERROR_CODES.ACTIVE_SUBSCRIPTION_CHECK_FAILED,
          `Failed to check active subscription: ${activeError.message}`,
          { userId, supabaseError: activeError },
          'high'
        ),
        queryCount: 1,
      };
    }

    return {
      success: true,
      data: hasActive,
      queryCount: 1,
    };

  } catch (error) {
    return {
      success: false,
      error: createValidationError(
        VALIDATION_ERROR_CODES.ACTIVE_SUBSCRIPTION_CHECK_FAILED,
        `Active subscription check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { userId, error: String(error) },
        'high'
      ),
      queryCount: 1,
    };
  }
}

/**
 * Get user's subscription tier
 */
async function getSubscriptionTier(userId: string): Promise<ValidationStepResult> {
  try {
    const { data: currentTier, error: tierError } = await supabase
      .rpc('get_user_subscription_tier', { p_user_id: userId });

    if (tierError) {
      console.error('Subscription tier check failed:', tierError);
      return {
        success: false,
        error: createValidationError(
          VALIDATION_ERROR_CODES.SUBSCRIPTION_TIER_CHECK_FAILED,
          `Failed to get subscription tier: ${tierError.message}`,
          { userId, supabaseError: tierError },
          'medium'
        ),
        queryCount: 1,
      };
    }

    return {
      success: true,
      data: currentTier,
      queryCount: 1,
    };

  } catch (error) {
    return {
      success: false,
      error: createValidationError(
        VALIDATION_ERROR_CODES.SUBSCRIPTION_TIER_CHECK_FAILED,
        `Subscription tier check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { userId, error: String(error) },
        'medium'
      ),
      queryCount: 1,
    };
  }
}

/**
 * Get subscription details
 */
async function getSubscriptionDetails(userId: string): Promise<ValidationStepResult> {
  try {
    const { data: subscription, error: subError } = await supabase
      .from('user_subscriptions')
      .select('end_date, tier, subscription_type')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('end_date', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subError) {
      console.error('Subscription details check failed:', subError);
      return {
        success: false,
        error: createValidationError(
          VALIDATION_ERROR_CODES.SUBSCRIPTION_DETAILS_CHECK_FAILED,
          `Failed to get subscription details: ${subError.message}`,
          { userId, supabaseError: subError },
          'medium'
        ),
        queryCount: 1,
      };
    }

    return {
      success: true,
      data: subscription,
      queryCount: 1,
    };

  } catch (error) {
    return {
      success: false,
      error: createValidationError(
        VALIDATION_ERROR_CODES.SUBSCRIPTION_DETAILS_CHECK_FAILED,
        `Subscription details check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { userId, error: String(error) },
        'medium'
      ),
      queryCount: 1,
    };
  }
}

/**
 * Validate subscription status and create final result
 */
function validateSubscriptionStatus(params: {
  userId: string;
  hasActive: boolean;
  currentTier: string;
  subscription: any;
  errors: any[];
}): { status: SubscriptionStatus; errors: any[] } {
  const { userId, hasActive, currentTier, subscription, errors } = params;

  // Validate subscription expiry
  const subscriptionExpiry = subscription?.end_date || null;
  const isExpired = subscriptionExpiry ? new Date(subscriptionExpiry) <= new Date() : false;

  if (hasActive && isExpired) {
    const warning = createValidationError(
      VALIDATION_ERROR_CODES.SUBSCRIPTION_EXPIRED,
      'User has active subscription flag but subscription is expired',
      { userId, subscriptionExpiry, hasActive },
      'medium'
    );
    errors.push(warning);
  }

  // Create final subscription status
  const validatedTier = validateTier(currentTier);
  const isValid = hasActive && !isExpired && validatedTier !== 'MEMBER';

  const status: SubscriptionStatus = {
    hasActiveSubscription: hasActive || false,
    currentTier: validatedTier,
    subscriptionExpiry,
    isValid,
    lastValidated: new Date().toISOString(),
    validationSource: 'database',
    warnings: errors.filter(e => e.severity === 'medium').map(e => e.message),
  };

  return { status, errors };
}
